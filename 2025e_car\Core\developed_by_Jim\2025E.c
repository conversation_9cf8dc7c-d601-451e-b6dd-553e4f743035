/**
 * @file 2025E.c
 * @brief 飞机巡航路径规划系统
 * <AUTHOR>
 * @date 2025
 *
 * 功能：在串口屏上实现飞机巡航路径显示
 * 地图：9×7网格（A1-A9, B1-B7），720×560像素，每格80×80像素
 * 策略：蛇形扫描法，遇到禁飞区向下绕行
 */

#include "2025E.h"
#include "oled.h"
#include "Waypoint.h"
#include <math.h>

// OLED显示管理
static uint8_t oled_line = 0; // 当前显示行
#define MAX_OLED_LINES 8      // OLED最大行数

// 显示模式控制
static uint8_t fast_mode = 0; // 0=正常模式(有延时), 1=快速模式(无延时)

// 地图参数定义
#define MAP_WIDTH 720  // 地图宽度(像素)
#define MAP_HEIGHT 560 // 地图高度(像素)
#define GRID_SIZE 80   // 每个格子大小(像素)
#define GRID_ROWS 7    // 网格行数(B1-B7)
#define GRID_COLS 9    // 网格列数(A1-A9)
#define TOTAL_GRIDS 63 // 总格子数

// 格子状态定义
#define GRID_NORMAL 0 // 正常格子
#define GRID_NO_FLY 1 // 禁飞区
#define GRID_START 2  // 起点

/**
 * @brief 格子坐标结构体
 */
typedef struct
{
    uint16_t center_x; // 格子中心点X坐标
    uint16_t center_y; // 格子中心点Y坐标
    uint8_t row;       // 行号 (0=B1, 1=B2, ..., 6=B7)
    uint8_t col;       // 列号 (0=A, 1=B, ..., 8=I)
    uint8_t status;    // 格子状态
    char name[4];      // 格子名称 (如"A1", "B7")
} GridCell_t;

/**
 * @brief 路径段结构体
 */
typedef struct
{
    uint16_t from_x, from_y; // 起点坐标
    uint16_t to_x, to_y;     // 终点坐标
    uint16_t color;          // 线条颜色
} PathSegment_t;

// 全局变量
static GridCell_t grid_map[GRID_ROWS][GRID_COLS]; // 网格地图
static PathSegment_t cruise_path[200];            // 巡航路径
static uint16_t path_count = 0;                   // 路径段数量
static uint8_t nofly_counter = 0;                 // 禁飞区计数器
static uint8_t map_initialized = 0;               // 地图初始化标志

/**
 * @brief OLED显示调试信息（替代printf）
 * @param msg 要显示的消息
 */
void OLED_DebugMsg(char *msg)
{
    if (oled_line >= MAX_OLED_LINES)
    {
        OLED_Clear(); // 清屏重新开始
        oled_line = 0;
    }

    OLED_ShowString(0, oled_line, msg, 12, 0);
    oled_line++;

    // 根据模式决定是否延时
    if (!fast_mode)
        HAL_Delay(50);
}

/**
 * @brief OLED显示数字信息
 * @param msg 消息前缀
 * @param num 要显示的数字
 */
void OLED_DebugNum(char *msg, uint32_t num)
{
    if (oled_line >= MAX_OLED_LINES)
    {
        OLED_Clear();
        oled_line = 0;
    }

    OLED_ShowString(0, oled_line, msg, 12, 0);
    OLED_ShowNum(60, oled_line, num, 5, 12, 0);
    oled_line++;

    if (!fast_mode)
        HAL_Delay(50);
}

/**
 * @brief OLED显示坐标信息
 * @param name 格子名称
 * @param x X坐标
 * @param y Y坐标
 */
void OLED_DebugCoord(char *name, uint16_t x, uint16_t y)
{
    if (oled_line >= MAX_OLED_LINES)
    {
        OLED_Clear();
        oled_line = 0;
    }

    OLED_ShowString(0, oled_line, name, 12, 0);
    OLED_ShowNum(30, oled_line, x, 3, 12, 0);
    OLED_ShowString(60, oled_line, ",", 12, 0);
    OLED_ShowNum(70, oled_line, y, 3, 12, 0);
    oled_line++;

    if (!fast_mode)
        HAL_Delay(30);
}

/**
 * @brief 清空OLED显示
 */
void OLED_ClearDebug(void)
{
    OLED_Clear();
    oled_line = 0;
}

/**
 * @brief 设置显示模式
 * @param mode 0=正常模式(有延时), 1=快速模式(无延时)
 */
void SetDisplayMode(uint8_t mode)
{
    fast_mode = mode;
    if (fast_mode)
    {
        OLED_DebugMsg("Fast Mode ON");
    }
    else
    {
        OLED_DebugMsg("Normal Mode");
    }
}

/**
 * @brief 初始化网格地图坐标
 * @note 按照你的思路：把64个方格的坐标封装好
 */
void InitGridMap(void)
{
    for (uint8_t row = 0; row < GRID_ROWS; row++)
    {
        for (uint8_t col = 0; col < GRID_COLS; col++)
        {
            // 计算格子中心坐标
            grid_map[row][col].center_x = col * GRID_SIZE + GRID_SIZE / 2;
            // Y坐标反向：B1在底部，B7在顶部
            grid_map[row][col].center_y = (GRID_ROWS - 1 - row) * GRID_SIZE + GRID_SIZE / 2;
            grid_map[row][col].row = row;
            grid_map[row][col].col = col;
            grid_map[row][col].status = GRID_NORMAL;

            // 生成格子名称 (A1, A2, ..., I7)
            sprintf(grid_map[row][col].name, "%c%d", 'A' + col, row + 1);
        }
    }

    // 设置起点（右下角）
    grid_map[GRID_ROWS - 1][GRID_COLS - 1].status = GRID_START;

    // OLED_DebugMsg("Grid Init OK");
    // OLED_DebugNum("Grids:", TOTAL_GRIDS);
}

/**
 * @brief 设置单个禁飞区格子
 * @param row 行号 (0-6)
 * @param col 列号 (0-8)
 */
void SetNoFlyZone(uint8_t row, uint8_t col)
{
    if (row >= GRID_ROWS || col >= GRID_COLS)
    {
        OLED_DebugMsg("NoFlsy: Out Bound");
        return;
    }

    // 设置单个格子为禁飞区
    grid_map[row][col].status = GRID_NO_FLY;
    OLED_DebugMsg("Set NoFly:");
    OLED_DebugMsg(grid_map[row][col].name);
}

/**
 * @brief 检查格子是否可访问
 * @param row 行号
 * @param col 列号
 * @return 1:可访问, 0:不可访问
 */
uint8_t IsGridAccessible(uint8_t row, uint8_t col)
{
    if (row >= GRID_ROWS || col >= GRID_COLS)
        return 0;

    return (grid_map[row][col].status != GRID_NO_FLY);
}

/**
 * @brief 添加路径段
 * @param from_x 起点X
 * @param from_y 起点Y
 * @param to_x 终点X
 * @param to_y 终点Y
 * @param color 线条颜色
 */
void AddPathSegment(uint16_t from_x, uint16_t from_y, uint16_t to_x, uint16_t to_y, uint16_t color)
{
    if (path_count < 200)
    {
        cruise_path[path_count].from_x = from_x;
        cruise_path[path_count].from_y = from_y;
        cruise_path[path_count].to_x = to_x;
        cruise_path[path_count].to_y = to_y;
        cruise_path[path_count].color = color;
        path_count++;
    }
}

/**
 * @brief 蛇形扫描巡航路径生成（核心算法）
 * @note 实现你的思路：路径规划变成选方格，遇到禁飞区就绕行
 * @return 生成的路径段数量
 */
uint16_t GenerateSnakeCruisePath(void)
{
    path_count = 0; // 重置路径计数
    uint16_t current_x, current_y;
    uint16_t next_x, next_y;
    uint8_t current_row, current_col;

    // 从起点开始（右下角A9,B1）
    current_row = 0;             // B1行（底部）
    current_col = GRID_COLS - 1; // A9列（最右）
    current_x = grid_map[current_row][current_col].center_x;
    current_y = grid_map[current_row][current_col].center_y;

    OLED_ClearDebug(); // 清屏开始显示路径生成过程
    OLED_DebugMsg("Gen Path Start:");
    OLED_DebugCoord(grid_map[current_row][current_col].name, current_x, current_y);

    // 蛇形扫描：从B1(row=0)往上到B7(row=6)，每行交替方向
    for (uint8_t row = 0; row < GRID_ROWS; row++)
    {
        if (row % 2 == 0) // 偶数行(B1,B3,B5,B7)：从右到左
        {
            for (int8_t col = GRID_COLS - 1; col >= 0; col--)
            {
                if (!IsGridAccessible(row, col))
                {
                    // 遇到禁飞区，垂直绕行（跳过整个禁飞区）
                    OLED_DebugMsg("NoFly Found:");
                    OLED_DebugMsg(grid_map[row][col].name);

                    // 找到禁飞区的结束位置（从右到左扫描）
                    int8_t bypass_col = col;
                    while (bypass_col >= 0 && !IsGridAccessible(row, bypass_col))
                    {
                        bypass_col--; // 继续向左找到禁飞区结束
                    }

                    // 如果禁飞区延伸到边缘，直接结束当前行
                    if (bypass_col < 0)
                    {
                        OLED_DebugMsg("Edge Row NoFly End");

                        // 直接结束当前行，不进行绕行
                        col = -1; // 结束当前行
                        continue;
                    }

                    if (row == 0) // B1在最底部，向上绕行
                    {
                        if (row < GRID_ROWS - 1 && IsGridAccessible(row + 1, bypass_col))
                        {
                            // 正确的绕行逻辑：
                            // 1. 垂直向上到绕行行（在当前位置）
                            uint16_t detour_x = current_x;
                            uint16_t detour_y = grid_map[row + 1][col].center_y;
                            AddPathSegment(current_x, current_y, detour_x, detour_y, 0xF800);

                            // 2. 水平移动跳过禁飞区（在绕行行）
                            uint16_t safe_x = grid_map[row + 1][bypass_col].center_x;
                            uint16_t safe_y = detour_y;
                            AddPathSegment(detour_x, detour_y, safe_x, safe_y, 0xF800);

                            // 3. 垂直回到原行（在禁飞区后面）
                            next_x = safe_x;
                            next_y = grid_map[row][bypass_col].center_y;
                            AddPathSegment(safe_x, safe_y, next_x, next_y, 0xF800);

                            current_x = next_x;
                            current_y = next_y;
                            OLED_DebugMsg("Up Bypass Done");

                            // 跳过整个禁飞区
                            col = bypass_col;
                        }
                    }
                    else // B2-B7都向下绕行
                    {
                        if (row > 0 && IsGridAccessible(row - 1, bypass_col))
                        {
                            // 正确的绕行逻辑：
                            // 1. 垂直向下到绕行行（在当前位置）
                            uint16_t detour_x = current_x;
                            uint16_t detour_y = grid_map[row - 1][col].center_y;
                            AddPathSegment(current_x, current_y, detour_x, detour_y, 0xF800);

                            // 2. 水平移动跳过禁飞区（在绕行行）
                            uint16_t safe_x = grid_map[row - 1][bypass_col].center_x;
                            uint16_t safe_y = detour_y;
                            AddPathSegment(detour_x, detour_y, safe_x, safe_y, 0xF800);

                            // 3. 垂直回到原行（在禁飞区后面）
                            next_x = safe_x;
                            next_y = grid_map[row][bypass_col].center_y;
                            AddPathSegment(safe_x, safe_y, next_x, next_y, 0xF800);

                            current_x = next_x;
                            current_y = next_y;
                            OLED_DebugMsg("Down Bypass Done");

                            // 跳过整个禁飞区
                            col = bypass_col;
                        }
                    }
                    continue; // 跳过禁飞区格子
                }

                // 正常格子，添加路径
                next_x = grid_map[row][col].center_x;
                next_y = grid_map[row][col].center_y;

                if (current_x != next_x || current_y != next_y) // 避免重复添加同一点
                {
                    AddPathSegment(current_x, current_y, next_x, next_y, 0x07E0); // 绿色正常路径
                    OLED_DebugMsg("Normal:");
                    OLED_DebugCoord(grid_map[row][col].name, next_x, next_y);
                }

                current_x = next_x;
                current_y = next_y;
            }
        }
        else // 奇数行(B2,B4,B6)：从左到右
        {
            for (uint8_t col = 0; col < GRID_COLS; col++)
            {
                if (!IsGridAccessible(row, col))
                {
                    // 遇到禁飞区，垂直绕行（跳过整个禁飞区）
                    OLED_DebugMsg("NoFly Found:");
                    OLED_DebugMsg(grid_map[row][col].name);

                    // 找到禁飞区的结束位置（从左到右扫描）
                    uint8_t bypass_col = col;
                    while (bypass_col < GRID_COLS && !IsGridAccessible(row, bypass_col))
                    {
                        bypass_col++; // 继续向右找到禁飞区结束
                    }

                    // 如果禁飞区延伸到边缘，直接结束当前行
                    if (bypass_col >= GRID_COLS)
                    {
                        OLED_DebugMsg("Edge Row NoFly End");

                        // 直接结束当前行，不进行绕行
                        col = GRID_COLS; // 结束当前行
                        continue;
                    }

                    if (row == 0) // B1在最底部，向上绕行
                    {
                        if (row < GRID_ROWS - 1 && IsGridAccessible(row + 1, bypass_col))
                        {
                            // 正确的绕行逻辑：
                            // 1. 垂直向上到绕行行（在当前位置）
                            uint16_t detour_x = current_x;
                            uint16_t detour_y = grid_map[row + 1][col].center_y;
                            AddPathSegment(current_x, current_y, detour_x, detour_y, 0xF800);

                            // 2. 水平移动跳过禁飞区（在绕行行）
                            uint16_t safe_x = grid_map[row + 1][bypass_col].center_x;
                            uint16_t safe_y = detour_y;
                            AddPathSegment(detour_x, detour_y, safe_x, safe_y, 0xF800);

                            // 3. 垂直回到原行（在禁飞区后面）
                            next_x = safe_x;
                            next_y = grid_map[row][bypass_col].center_y;
                            AddPathSegment(safe_x, safe_y, next_x, next_y, 0xF800);

                            current_x = next_x;
                            current_y = next_y;
                            OLED_DebugMsg("Up Bypass Done");

                            // 跳过整个禁飞区
                            col = bypass_col - 1; // -1是因为for循环会++
                        }
                    }
                    else // B2-B7都向下绕行
                    {
                        if (row > 0 && IsGridAccessible(row - 1, bypass_col))
                        {
                            // 正确的绕行逻辑：
                            // 1. 垂直向下到绕行行（在当前位置）
                            uint16_t detour_x = current_x;
                            uint16_t detour_y = grid_map[row - 1][col].center_y;
                            AddPathSegment(current_x, current_y, detour_x, detour_y, 0xF800);

                            // 2. 水平移动跳过禁飞区（在绕行行）
                            uint16_t safe_x = grid_map[row - 1][bypass_col].center_x;
                            uint16_t safe_y = detour_y;
                            AddPathSegment(detour_x, detour_y, safe_x, safe_y, 0xF800);

                            // 3. 垂直回到原行（在禁飞区后面）
                            next_x = safe_x;
                            next_y = grid_map[row][bypass_col].center_y;
                            AddPathSegment(safe_x, safe_y, next_x, next_y, 0xF800);

                            current_x = next_x;
                            current_y = next_y;
                            OLED_DebugMsg("Down Bypass Done");

                            // 跳过整个禁飞区
                            col = bypass_col - 1; // -1是因为for循环会++
                        }
                    }
                    continue; // 跳过禁飞区格子
                }

                // 正常格子，添加路径
                next_x = grid_map[row][col].center_x;
                next_y = grid_map[row][col].center_y;

                if (current_x != next_x || current_y != next_y) // 避免重复添加同一点
                {
                    AddPathSegment(current_x, current_y, next_x, next_y, 0x07E0); // 绿色正常路径
                    OLED_DebugMsg("Normal:");
                    OLED_DebugCoord(grid_map[row][col].name, next_x, next_y);
                }

                current_x = next_x;
                current_y = next_y;
            }
        }
    }

    OLED_DebugMsg("Path Gen Done");
    OLED_DebugNum("Segments:", path_count);
    return path_count;
}

/**
 * @brief 发送画线指令到串口屏
 * @param x1 起点X坐标
 * @param y1 起点Y坐标
 * @param x2 终点X坐标
 * @param y2 终点Y坐标
 * @param color 线条颜色
 * @note 指令格式："line %d,%d,%d,%d,%d\xFF\xFF\xFF"
 */
void SendLineCommand(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color)
{
    char cmd_buffer[64];
    color=16868;
    // 构造画线指令 - 使用传入的color参数，不强制覆盖
    sprintf(cmd_buffer, "line %d,%d,%d,%d,%d\xFF\xFF\xFF", x1, y1, x2, y2, color);
    tjc_send_string(cmd_buffer);
    // 这里应该调用你的串口发送函数
    // 例如：HAL_UART_Transmit(&huart_screen, (uint8_t*)cmd_buffer, strlen(cmd_buffer), 1000);

    // 在OLED上显示画线指令信息
    OLED_DebugMsg("Send Line Cmd");
    OLED_DebugCoord("From", x1, y1);
    OLED_DebugCoord("To", x2, y2);
}

/**
 * @brief 执行巡航路径显示
 * @note 将生成的路径发送到串口屏显示
 */
void ExecuteCruisePathDisplay(void)
{
    OLED_ClearDebug();
    OLED_DebugMsg("Exec Path Display");

    for (uint16_t i = 0; i < path_count; i++)
    {
        SendLineCommand(cruise_path[i].from_x, cruise_path[i].from_y,
                        cruise_path[i].to_x, cruise_path[i].to_y,
                        cruise_path[i].color);

        // 在快速模式下，只显示关键进度
        if (!fast_mode || (i % 10 == 0)) // 快速模式下每10个显示一次
        {
            OLED_DebugNum("Progress:", i + 1);
        }

        // 根据模式调整延时
        if (!fast_mode)
        {
            HAL_Delay(20); // 正常模式：慢速显示
        }
        else
        {
            HAL_Delay(2); // 快速模式：最小延时
        }
    }

    OLED_DebugMsg("Display Done!");
}

/**
 * @brief 在OLED上显示网格地图信息（调试用）
 */
void PrintGridMapInfo(void)
{
    OLED_ClearDebug();
    OLED_DebugMsg("=== Grid Map ===");
    OLED_DebugNum("Size:", MAP_WIDTH);
    OLED_DebugNum("Grid:", GRID_SIZE);
    OLED_DebugNum("Rows:", GRID_ROWS);
    OLED_DebugNum("Cols:", GRID_COLS);

    if (!fast_mode)
        HAL_Delay(500); // 让用户看到基本信息

    // 显示禁飞区信息
    OLED_ClearDebug();
    OLED_DebugMsg("NoFly Zones:");

    uint8_t nofly_count = 0;
    for (uint8_t row = 0; row < GRID_ROWS; row++)
    {
        for (uint8_t col = 0; col < GRID_COLS; col++)
        {
            if (grid_map[row][col].status == GRID_NO_FLY)
            {
                OLED_DebugCoord(grid_map[row][col].name,
                                grid_map[row][col].center_x,
                                grid_map[row][col].center_y);
                nofly_count++;
            }
        }
    }

    if (nofly_count == 0)
    {
        OLED_DebugMsg("No NoFly Zone");
    }

    if (!fast_mode)
        HAL_Delay(500); // 让用户看到禁飞区信息
}

/**
 * @brief 巡航系统测试函数
 * @note 完整的测试流程，演示你的思路实现
 */
void CruiseSystemTest(void)
{
    OLED_ClearDebug();
    OLED_DebugMsg("=== Cruise Test ===");
    if (!fast_mode)
        HAL_Delay(200);

    // 1. 初始化网格地图
    OLED_ClearDebug();
    OLED_DebugMsg("Step1: Init Grid");
    InitGridMap();
    if (!fast_mode)
        HAL_Delay(300);

    // 2. 设置禁飞区（示例：在B3行设置A3-A5为禁飞区）
    OLED_ClearDebug();
    OLED_DebugMsg("Step2: Set NoFly");
    SetNoFlyZone(2, 2); // B3行，从A3开始的3个格子
    if (!fast_mode)
        HAL_Delay(300);

    // 3. 打印地图信息（快速模式下跳过）
    if (!fast_mode)
    {
        OLED_ClearDebug();
        OLED_DebugMsg("Step3: Show Info");
        PrintGridMapInfo();
    }

    // 4. 生成巡航路径
    OLED_ClearDebug();
    OLED_DebugMsg("Step4: Gen Path");
    uint16_t path_segments = GenerateSnakeCruisePath();
    if (!fast_mode)
        HAL_Delay(300);

    // 5. 执行路径显示
    OLED_ClearDebug();
    OLED_DebugMsg("Step5: Display");
    ExecuteCruisePathDisplay();

    // 测试完成总结
    OLED_ClearDebug();
    OLED_DebugMsg("=== Test Done ===");
    OLED_DebugNum("Segments:", path_segments);
    OLED_DebugNum("Covered:", TOTAL_GRIDS - 3); // 减去3个禁飞区格子
}

/**
 * @brief 获取指定格子的坐标
 * @param row 行号 (0-6)
 * @param col 列号 (0-8)
 * @param x 返回X坐标
 * @param y 返回Y坐标
 * @return 1:成功, 0:失败
 */
uint8_t GetGridCoordinate(uint8_t row, uint8_t col, uint16_t *x, uint16_t *y)
{
    if (row >= GRID_ROWS || col >= GRID_COLS)
        return 0;

    *x = grid_map[row][col].center_x;
    *y = grid_map[row][col].center_y;
    return 1;
}

/**
 * @brief 清空巡航路径
 */
void ClearCruisePath(void)
{
    path_count = 0;
    OLED_DebugMsg("Path Cleared");
}

/**
 * @brief 纵向蛇形扫描巡航路径生成（处理纵向禁飞区）
 * @note 按列扫描，从A9B1开始向上，遇到纵向禁飞区进行水平绕行
 * @return 生成的路径段数量
 */
uint16_t GenerateVerticalSnakeCruisePath(void)
{
    path_count = 0; // 重置路径计数
    uint16_t current_x, current_y;
    uint16_t next_x, next_y;
    uint8_t current_row, current_col;

    // 从起点开始（右下角A9,B1）
    current_row = 0;             // B1行（底部）
    current_col = GRID_COLS - 1; // A9列（最右）
    current_x = grid_map[current_row][current_col].center_x;
    current_y = grid_map[current_row][current_col].center_y;

    OLED_ClearDebug(); // 清屏开始显示路径生成过程
    OLED_DebugMsg("Gen Vertical Path:");
    OLED_DebugCoord(grid_map[current_row][current_col].name, current_x, current_y);

    // 纵向蛇形扫描：从A9(col=8)往左到A1(col=0)，每列交替方向
    for (int8_t col = GRID_COLS - 1; col >= 0; col--)
    {
        if ((GRID_COLS - 1 - col) % 2 == 0) // 偶数列(A9,A7,A5...)：从下到上
        {
            for (uint8_t row = 0; row < GRID_ROWS; row++)
            {
                if (!IsGridAccessible(row, col))
                {
                    // 遇到纵向禁飞区，水平绕行
                    OLED_DebugMsg("Vertical NoFly:");
                    OLED_DebugMsg(grid_map[row][col].name);

                    // 找到纵向禁飞区的结束位置（从下到上扫描）
                    uint8_t bypass_row = row;
                    while (bypass_row < GRID_ROWS && !IsGridAccessible(bypass_row, col))
                    {
                        bypass_row++; // 继续向上找到禁飞区结束
                    }

                    // 如果禁飞区延伸到顶部边缘，直接结束当前列
                    if (bypass_row >= GRID_ROWS)
                    {
                        OLED_DebugMsg("Top Edge Col NoFly End");

                        // 直接结束当前列，不进行绕行
                        row = GRID_ROWS; // 结束当前列
                        continue;
                    }

                    // 水平绕行逻辑：向右绕行（如果可能）
                    if (col < GRID_COLS - 1 && IsGridAccessible(row, col + 1))
                    {
                        // 1. 水平向右到绕行列（在当前位置）
                        uint16_t detour_x = grid_map[row][col + 1].center_x;
                        uint16_t detour_y = current_y;
                        AddPathSegment(current_x, current_y, detour_x, detour_y, 0xF800);

                        // 2. 垂直移动跳过禁飞区（在绕行列）
                        uint16_t safe_x = detour_x;
                        uint16_t safe_y = grid_map[bypass_row][col + 1].center_y;
                        AddPathSegment(detour_x, detour_y, safe_x, safe_y, 0xF800);

                        // 3. 水平回到原列（在禁飞区后面）
                        next_x = grid_map[bypass_row][col].center_x;
                        next_y = safe_y;
                        AddPathSegment(safe_x, safe_y, next_x, next_y, 0xF800);

                        current_x = next_x;
                        current_y = next_y;
                        OLED_DebugMsg("Right Bypass Done");

                        // 跳过整个纵向禁飞区
                        row = bypass_row - 1; // -1是因为for循环会++
                    }
                    else if (col > 0 && IsGridAccessible(row, col - 1)) // 向左绕行
                    {
                        // 1. 水平向左到绕行列（在当前位置）
                        uint16_t detour_x = grid_map[row][col - 1].center_x;
                        uint16_t detour_y = current_y;
                        AddPathSegment(current_x, current_y, detour_x, detour_y, 0xF800);

                        // 2. 垂直移动跳过禁飞区（在绕行列）
                        uint16_t safe_x = detour_x;
                        uint16_t safe_y = grid_map[bypass_row][col - 1].center_y;
                        AddPathSegment(detour_x, detour_y, safe_x, safe_y, 0xF800);

                        // 3. 水平回到原列（在禁飞区后面）
                        next_x = grid_map[bypass_row][col].center_x;
                        next_y = safe_y;
                        AddPathSegment(safe_x, safe_y, next_x, next_y, 0xF800);

                        current_x = next_x;
                        current_y = next_y;
                        OLED_DebugMsg("Left Bypass Done");

                        // 跳过整个纵向禁飞区
                        row = bypass_row - 1; // -1是因为for循环会++
                    }
                    else
                    {
                        // 左右都不能绕行的边缘情况处理（如A9列或A1列）
                        OLED_DebugMsg("Edge Col NoFly End");

                        // 直接结束当前列，不进行绕行
                        row = GRID_ROWS; // 结束当前列
                    }
                    continue; // 跳过禁飞区格子
                }

                // 正常格子，添加路径
                next_x = grid_map[row][col].center_x;
                next_y = grid_map[row][col].center_y;

                if (current_x != next_x || current_y != next_y) // 避免重复添加同一点
                {
                    AddPathSegment(current_x, current_y, next_x, next_y, 0x07E0); // 绿色正常路径
                    OLED_DebugMsg("Normal:");
                    OLED_DebugCoord(grid_map[row][col].name, next_x, next_y);
                }

                current_x = next_x;
                current_y = next_y;
            }
        }
        else // 奇数列(A8,A6,A4...)：从上到下
        {
            for (int8_t row = GRID_ROWS - 1; row >= 0; row--)
            {
                if (!IsGridAccessible(row, col))
                {
                    // 遇到纵向禁飞区，水平绕行
                    OLED_DebugMsg("Vertical NoFly:");
                    OLED_DebugMsg(grid_map[row][col].name);

                    // 找到纵向禁飞区的结束位置（从上到下扫描）
                    int8_t bypass_row = row;
                    while (bypass_row >= 0 && !IsGridAccessible(bypass_row, col))
                    {
                        bypass_row--; // 继续向下找到禁飞区结束
                    }

                    // 如果禁飞区延伸到底部边缘，直接结束当前列
                    if (bypass_row < 0)
                    {
                        OLED_DebugMsg("Bottom Edge Col NoFly End");

                        // 直接结束当前列，不进行绕行
                        row = -1; // 结束当前列
                        continue;
                    }

                    // 水平绕行逻辑：向右绕行（如果可能）
                    if (col < GRID_COLS - 1 && IsGridAccessible(row, col + 1))
                    {
                        // 1. 水平向右到绕行列（在当前位置）
                        uint16_t detour_x = grid_map[row][col + 1].center_x;
                        uint16_t detour_y = current_y;
                        AddPathSegment(current_x, current_y, detour_x, detour_y, 0xF800);

                        // 2. 垂直移动跳过禁飞区（在绕行列）
                        uint16_t safe_x = detour_x;
                        uint16_t safe_y = grid_map[bypass_row][col + 1].center_y;
                        AddPathSegment(detour_x, detour_y, safe_x, safe_y, 0xF800);

                        // 3. 水平回到原列（在禁飞区后面）
                        next_x = grid_map[bypass_row][col].center_x;
                        next_y = safe_y;
                        AddPathSegment(safe_x, safe_y, next_x, next_y, 0xF800);

                        current_x = next_x;
                        current_y = next_y;
                        OLED_DebugMsg("Right Bypass Done");

                        // 跳过整个纵向禁飞区
                        row = bypass_row + 1; // +1是因为for循环会--
                    }
                    else if (col > 0 && IsGridAccessible(row, col - 1)) // 向左绕行
                    {
                        // 1. 水平向左到绕行列（在当前位置）
                        uint16_t detour_x = grid_map[row][col - 1].center_x;
                        uint16_t detour_y = current_y;
                        AddPathSegment(current_x, current_y, detour_x, detour_y, 0xF800);

                        // 2. 垂直移动跳过禁飞区（在绕行列）
                        uint16_t safe_x = detour_x;
                        uint16_t safe_y = grid_map[bypass_row][col - 1].center_y;
                        AddPathSegment(detour_x, detour_y, safe_x, safe_y, 0xF800);

                        // 3. 水平回到原列（在禁飞区后面）
                        next_x = grid_map[bypass_row][col].center_x;
                        next_y = safe_y;
                        AddPathSegment(safe_x, safe_y, next_x, next_y, 0xF800);

                        current_x = next_x;
                        current_y = next_y;
                        OLED_DebugMsg("Left Bypass Done");

                        // 跳过整个纵向禁飞区
                        row = bypass_row + 1; // +1是因为for循环会--
                    }
                    else
                    {
                        // 左右都不能绕行的边缘情况处理（如A9列或A1列）
                        OLED_DebugMsg("Edge Col NoFly End");

                        // 直接结束当前列，不进行绕行
                        row = -1; // 结束当前列
                    }
                    continue; // 跳过禁飞区格子
                }

                // 正常格子，添加路径
                next_x = grid_map[row][col].center_x;
                next_y = grid_map[row][col].center_y;

                if (current_x != next_x || current_y != next_y) // 避免重复添加同一点
                {
                    AddPathSegment(current_x, current_y, next_x, next_y, 0x07E0); // 绿色正常路径
                    OLED_DebugMsg("Normal:");
                    OLED_DebugCoord(grid_map[row][col].name, next_x, next_y);
                }

                current_x = next_x;
                current_y = next_y;
            }
        }
    }

    OLED_DebugMsg("Vertical Path Done");
    OLED_DebugNum("Segments:", path_count);
    return path_count;
}

/**
 * @brief 设置纵向禁飞区（三个连续格子）
 * @param start_row 起始行号 (0-6)
 * @param col 列号 (0-8)
 */
void SetVerticalNoFlyZone(uint8_t start_row, uint8_t col)
{
    if (start_row >= GRID_ROWS - 2 || col >= GRID_COLS) // 确保有足够空间设置3个格子
    {
        OLED_DebugMsg("Vertical NoFly: Out Bound");
        return;
    }

    grid_map[row][col].status = GRID_NO_FLY;
}

/**
 * @brief 纵向禁飞区巡航系统测试函数
 * @note 测试纵向三个连续禁飞区的处理
 */
void VerticalCruiseSystemTest(void)
{
    OLED_ClearDebug();
    OLED_DebugMsg("=== Vertical Test ===");
    if (!fast_mode)
        HAL_Delay(200);

    // 1. 初始化网格地图
    OLED_ClearDebug();
    OLED_DebugMsg("Step1: Init Grid");
    InitGridMap();
    if (!fast_mode)
        HAL_Delay(300);

    // 2. 设置纵向禁飞区（示例：在A6列设置B2-B4为禁飞区）
    OLED_ClearDebug();
    OLED_DebugMsg("Step2: Set Vertical NoFly");
    SetVerticalNoFlyZone(1, 5); // 从B2开始，A6列的3个格子
    if (!fast_mode)
        HAL_Delay(300);

    // 3. 打印地图信息（快速模式下跳过）
    if (!fast_mode)
    {
        OLED_ClearDebug();
        OLED_DebugMsg("Step3: Show Info");
        PrintGridMapInfo();
    }

    // 4. 生成纵向巡航路径
    OLED_ClearDebug();
    OLED_DebugMsg("Step4: Gen Vertical Path");
    uint16_t path_segments = GenerateVerticalSnakeCruisePath();
    if (!fast_mode)
        HAL_Delay(300);

    // 5. 执行路径显示
    OLED_ClearDebug();
    OLED_DebugMsg("Step5: Display");
    ExecuteCruisePathDisplay();

    // 测试完成总结
    OLED_ClearDebug();
    OLED_DebugMsg("=== Vertical Done ===");
    OLED_DebugNum("Segments:", path_segments);
    OLED_DebugNum("Covered:", TOTAL_GRIDS - 3); // 减去3个禁飞区格子
}

/**
 * @brief 快速巡航测试（无延时，直接画线）
 */
void FastCruiseTest(void)
{
    // 设置快速模式
    SetDisplayMode(1);

    // 执行快速测试
    OLED_ClearDebug();
    OLED_DebugMsg("=== Fast Test ===");

    // 初始化和设置（无延时）
    InitGridMap();
    SetNoFlyZone(2, 2); // B3行，从A3开始的3个格子

    // 生成路径（无延时）
    OLED_DebugMsg("Generating...");
    uint16_t path_segments = GenerateSnakeCruisePath();

    // 快速画线
    OLED_DebugMsg("Drawing Lines...");
    ExecuteCruisePathDisplay();

    // 显示结果
    OLED_ClearDebug();
    OLED_DebugMsg("=== Fast Done ===");
    OLED_DebugNum("Segments:", path_segments);
    OLED_DebugNum("Covered:", TOTAL_GRIDS - 3);

    // 恢复正常模式
    SetDisplayMode(0);
}

/**
 * @brief 简单坐标转换函数
 * @param lie_str 列坐标字符串，如"A3"
 * @param hang_str 行坐标字符串，如"B2"
 * @param row 输出行号(0-6)
 * @param col 输出列号(0-8)
 */
void ConvertCoord(char *lie_str, char *hang_str, uint8_t *row, uint8_t *col)
{
    // 转换列坐标 A1-A9 -> 0-8
    if (lie_str[0] == 'A' && lie_str[1] >= '1' && lie_str[1] <= '9')
    {
        *col = lie_str[1] - '1';
    }

    // 转换行坐标 B1-B7 -> 0-6
    if (hang_str[0] == 'B' && hang_str[1] >= '1' && hang_str[1] <= '7')
    {
        *row = hang_str[1] - '1';
    }
}

/**
 * @brief 快速测试纵向巡航（无延时）
 */
void QuickVerticalTest(void)
{
    // 设置快速模式
    SetDisplayMode(1);

    OLED_Clear();
    OLED_ShowString(0, 0, "Quick Vertical Test", 12, 0);

    // 初始化和设置
    InitGridMap();
    SetVerticalNoFlyZone(4, 0); // A6列，B2-B4

    // 生成和显示路径
    uint16_t segments = GenerateVerticalSnakeCruisePath();
    ExecuteCruisePathDisplay();

    OLED_ShowString(0, 7, "Quick Test Done", 12, 0);

    // 恢复正常模式
    SetDisplayMode(0);
}
